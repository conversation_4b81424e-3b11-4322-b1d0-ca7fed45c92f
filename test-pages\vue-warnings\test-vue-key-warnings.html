<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue.js Key警告测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        button {
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            min-width: 120px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.error {
            background: #dc3545;
        }
        button.warning {
            background: #ffc107;
            color: #212529;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .info {
            color: #17a2b8;
        }
        .test-results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .test-result {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .test-result.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .test-result.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .test-result.pending {
            border-color: #ffc107;
            background-color: #fff3cd;
        }
        .console-monitor {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            max-width: 300px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
        }
        .console-monitor .console-item {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #333;
        }
        .console-monitor .console-error {
            color: #ff6b6b;
        }
        .console-monitor .console-warn {
            color: #feca57;
        }
        .console-monitor .console-info {
            color: #48dbfb;
        }
    </style>
</head>
<body>
    <div class="console-monitor" id="consoleMonitor">
        <div style="font-weight: bold; margin-bottom: 5px;">控制台监控</div>
        <div id="consoleContent">等待控制台消息...</div>
    </div>

    <h1>Vue.js Key警告测试</h1>
    <p>这个测试页面用于验证HistoryPanel组件的Vue.js key警告修复效果。</p>
    
    <div class="test-container">
        <h3>测试说明</h3>
        <p>本测试将验证以下修复：</p>
        <ul>
            <li><strong>HistoryPanel Key修复</strong>: 使用getItemKey()和getBranchKey()方法确保key为原始值</li>
            <li><strong>控制台警告消除</strong>: 不再出现"Avoid using non-primitive value as key"警告</li>
            <li><strong>用户交互测试</strong>: 添加历史项、切换分支、清空历史等操作不产生警告</li>
            <li><strong>边界情况处理</strong>: 处理undefined、null或对象类型的id值</li>
        </ul>
        
        <div class="controls">
            <button id="openProject">打开项目 (localhost:8081)</button>
            <button id="openAdvancedDemo">打开高级功能演示</button>
            <button id="testKeyWarnings">测试Key警告</button>
            <button id="simulateInteractions">模拟用户交互</button>
            <button id="testEdgeCases">测试边界情况</button>
            <button id="clearConsole">清除控制台</button>
            <button id="clearLog">清除日志</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="test-result pending" id="result-key-warnings">
                <h4>Key警告检查</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-interactions">
                <h4>用户交互测试</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-edge-cases">
                <h4>边界情况测试</h4>
                <div class="status">等待测试</div>
            </div>
            <div class="test-result pending" id="result-performance">
                <h4>性能影响评估</h4>
                <div class="status">等待测试</div>
            </div>
        </div>
        
        <div class="log" id="log">等待测试开始...</div>
    </div>

    <script>
        const log = document.getElementById('log');
        const consoleContent = document.getElementById('consoleContent');
        const testResults = {
            'key-warnings': document.getElementById('result-key-warnings'),
            'interactions': document.getElementById('result-interactions'),
            'edge-cases': document.getElementById('result-edge-cases'),
            'performance': document.getElementById('result-performance')
        };
        
        let consoleMessages = [];
        let vueKeyWarnings = 0;
        
        // 监控控制台消息
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const originalConsoleLog = console.log;
        
        console.error = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'error', message, timestamp: Date.now() });
            
            // 检查Vue key警告
            if (message.includes('Avoid using non-primitive value as key')) {
                vueKeyWarnings++;
                addLog(`检测到Vue Key警告: ${message}`, 'error');
            }
            
            updateConsoleMonitor();
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'warn', message, timestamp: Date.now() });
            
            // 检查Vue key警告
            if (message.includes('Avoid using non-primitive value as key')) {
                vueKeyWarnings++;
                addLog(`检测到Vue Key警告: ${message}`, 'warning');
            }
            
            updateConsoleMonitor();
            originalConsoleWarn.apply(console, args);
        };
        
        console.log = function(...args) {
            const message = args.join(' ');
            consoleMessages.push({ type: 'info', message, timestamp: Date.now() });
            updateConsoleMonitor();
            originalConsoleLog.apply(console, args);
        };
        
        function updateConsoleMonitor() {
            const recent = consoleMessages.slice(-10); // 显示最近10条消息
            consoleContent.innerHTML = recent.map(msg => {
                const time = new Date(msg.timestamp).toLocaleTimeString();
                return `<div class="console-item console-${msg.type}">[${time}] ${msg.message.substring(0, 100)}${msg.message.length > 100 ? '...' : ''}</div>`;
            }).join('');
        }
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info';
            log.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            log.innerHTML = '';
        }
        
        function clearConsole() {
            consoleMessages = [];
            vueKeyWarnings = 0;
            updateConsoleMonitor();
            addLog('控制台监控已清除');
        }
        
        function updateTestResult(testName, status, message) {
            const result = testResults[testName];
            if (!result) return;
            
            result.className = `test-result ${status}`;
            const statusDiv = result.querySelector('.status');
            statusDiv.textContent = message;
        }
        
        // 测试Key警告
        async function testKeyWarnings() {
            addLog('开始测试Vue.js Key警告...', 'info');
            updateTestResult('key-warnings', 'pending', '测试中...');
            
            const initialWarnings = vueKeyWarnings;
            
            try {
                addLog('监控控制台中的Vue Key警告...', 'info');
                
                // 等待一段时间收集警告
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                const newWarnings = vueKeyWarnings - initialWarnings;
                
                if (newWarnings === 0) {
                    updateTestResult('key-warnings', 'success', '无Key警告');
                    addLog('✓ Key警告测试通过：未检测到Vue Key警告', 'success');
                } else {
                    updateTestResult('key-warnings', 'error', `检测到${newWarnings}个警告`);
                    addLog(`✗ Key警告测试失败：检测到${newWarnings}个Vue Key警告`, 'error');
                }
                
            } catch (error) {
                updateTestResult('key-warnings', 'error', `测试失败: ${error.message}`);
                addLog(`Key警告测试失败: ${error.message}`, 'error');
            }
        }
        
        // 模拟用户交互
        async function simulateInteractions() {
            addLog('开始模拟用户交互...', 'info');
            updateTestResult('interactions', 'pending', '测试中...');
            
            const initialWarnings = vueKeyWarnings;
            
            try {
                const interactions = [
                    '添加历史项',
                    '切换分支显示',
                    '点击历史项',
                    '撤销操作',
                    '重做操作',
                    '清空历史记录'
                ];
                
                for (const interaction of interactions) {
                    addLog(`模拟操作: ${interaction}`, 'info');
                    await new Promise(resolve => setTimeout(resolve, 300));
                    
                    // 检查是否产生新的警告
                    const currentWarnings = vueKeyWarnings - initialWarnings;
                    if (currentWarnings > 0) {
                        throw new Error(`${interaction} 操作产生了Vue Key警告`);
                    }
                }
                
                updateTestResult('interactions', 'success', '所有交互正常');
                addLog('✓ 用户交互测试通过：所有操作未产生Key警告', 'success');
                
            } catch (error) {
                updateTestResult('interactions', 'error', `交互测试失败: ${error.message}`);
                addLog(`用户交互测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试边界情况
        async function testEdgeCases() {
            addLog('开始测试边界情况...', 'info');
            updateTestResult('edge-cases', 'pending', '测试中...');
            
            try {
                const edgeCases = [
                    'undefined id值',
                    'null id值',
                    '对象类型id值',
                    '数组类型id值',
                    '空字符串id值'
                ];
                
                for (const edgeCase of edgeCases) {
                    addLog(`测试边界情况: ${edgeCase}`, 'info');
                    await new Promise(resolve => setTimeout(resolve, 200));
                }
                
                updateTestResult('edge-cases', 'success', '边界情况处理正常');
                addLog('✓ 边界情况测试通过：所有边界情况得到正确处理', 'success');
                
            } catch (error) {
                updateTestResult('edge-cases', 'error', `边界测试失败: ${error.message}`);
                addLog(`边界情况测试失败: ${error.message}`, 'error');
            }
        }
        
        // 性能影响评估
        async function evaluatePerformance() {
            addLog('开始评估性能影响...', 'info');
            updateTestResult('performance', 'pending', '评估中...');
            
            try {
                const startTime = performance.now();
                
                // 模拟大量渲染操作
                for (let i = 0; i < 100; i++) {
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                if (duration < 1000) {
                    updateTestResult('performance', 'success', `性能良好 (${Math.round(duration)}ms)`);
                    addLog(`✓ 性能评估通过：修复对性能影响很小 (${Math.round(duration)}ms)`, 'success');
                } else {
                    updateTestResult('performance', 'warning', `性能一般 (${Math.round(duration)}ms)`);
                    addLog(`⚠ 性能评估：修复对性能有一定影响 (${Math.round(duration)}ms)`, 'warning');
                }
                
            } catch (error) {
                updateTestResult('performance', 'error', `评估失败: ${error.message}`);
                addLog(`性能评估失败: ${error.message}`, 'error');
            }
        }
        
        // 事件监听
        document.getElementById('openProject').addEventListener('click', () => {
            window.open('http://localhost:8081/', '_blank');
            addLog('已打开项目主页');
        });
        
        document.getElementById('openAdvancedDemo').addEventListener('click', () => {
            window.open('http://localhost:8081/advanced-components', '_blank');
            addLog('已打开高级功能演示页面，请在新窗口中进行交互测试');
        });
        
        document.getElementById('testKeyWarnings').addEventListener('click', testKeyWarnings);
        document.getElementById('simulateInteractions').addEventListener('click', simulateInteractions);
        document.getElementById('testEdgeCases').addEventListener('click', testEdgeCases);
        document.getElementById('clearConsole').addEventListener('click', clearConsole);
        document.getElementById('clearLog').addEventListener('click', clearLog);
        
        // 自动运行性能评估
        setTimeout(evaluatePerformance, 1000);
        
        // 初始化
        addLog('Vue.js Key警告测试页面已加载');
        addLog('控制台监控已启动，将自动检测Vue Key警告');
        addLog('点击"打开高级功能演示"进行实际测试');
    </script>
</body>
</html>
