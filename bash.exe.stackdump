Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FE8E
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210286019, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E80  000210068E24 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9160  00021006A225 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEBD3A0000 ntdll.dll
7FFEBC820000 KERNEL32.DLL
7FFEBA910000 KERNELBASE.dll
7FFEBD150000 USER32.dll
7FFEBA840000 win32u.dll
000210040000 msys-2.0.dll
7FFEBCAA0000 GDI32.dll
7FFEBB000000 gdi32full.dll
7FFEBA5B0000 msvcp_win.dll
7FFEBA6F0000 ucrtbase.dll
7FFEBCFE0000 advapi32.dll
7FFEBCAD0000 msvcrt.dll
7FFEBB640000 sechost.dll
7FFEBC700000 RPCRT4.dll
7FFEB9A00000 CRYPTBASE.DLL
7FFEBA870000 bcryptPrimitives.dll
7FFEBB870000 IMM32.DLL
