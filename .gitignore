# ===================================
# Node.js 依赖和包管理
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
package-lock.json

# ===================================
# 构建输出目录
# ===================================
dist/
build/
out/
.output/
.vercel/
.netlify/

# ===================================
# 环境变量和配置文件
# ===================================
.env
.env.local
.env.*.local
.env.development.local
.env.test.local
.env.production.local
.env.staging

# ===================================
# IDE和编辑器文件
# ===================================
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# Sublime Text
*.sublime-workspace
*.sublime-project

# ===================================
# 操作系统生成的文件
# ===================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon

# Windows
ehthumbs.db
Thumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~
.directory
.Trash-*

# ===================================
# 测试覆盖率和测试结果
# ===================================
coverage/
*.lcov
.nyc_output
test-results/
jest-coverage/
junit.xml
test-report.xml

# ===================================
# 日志文件
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===================================
# 临时文件和缓存
# ===================================
tmp/
temp/
*.tmp
*.bak
*.swp
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# ===================================
# 运行时文件
# ===================================
pids
*.pid
*.seed
*.pid.lock

# ===================================
# 构建和分析工具
# ===================================
bundle-analyzer-report.html
webpack-bundle-analyzer-report.html
.webpack/
.rollup.cache/

# ===================================
# 测试工具
# ===================================
# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# ===================================
# 性能和监控
# ===================================
.lighthouse/
.speedcurve/

# ===================================
# 文档构建文件 (保留markdown文档)
# ===================================
docs/build/
docs/.vuepress/dist/
docs/.vitepress/dist/

# ===================================
# 移动端和混合应用
# ===================================
# Cordova
platforms/
plugins/
www/

# React Native
.expo/
.expo-shared/

# ===================================
# 数据库文件
# ===================================
*.sqlite
*.sqlite3
*.db

# ===================================
# 安全和密钥文件
# ===================================
*.pem
*.key
*.crt
*.p12
.secrets/

# ===================================
# 备份文件
# ===================================
*.backup
*.bak
*.orig

# ===================================
# 压缩文件
# ===================================
*.zip
*.tar.gz
*.rar
*.7z

# ===================================
# 项目特定忽略
# ===================================
# 图像编辑器临时文件
*.psd~
*.ai~
*.sketch~

# 大型测试图像文件
test-images/large/
sample-images/raw/

# 性能测试结果
performance-reports/
memory-dumps/
