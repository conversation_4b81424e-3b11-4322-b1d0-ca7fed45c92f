# 可拖拽监控控件功能

## 概述

PhotoEditor Demo项目现在支持可拖拽的监控控件，包括系统状态监控和性能监控控件。这些控件可以自由拖拽、边缘吸附和自动避免冲突，大大提升了用户界面的灵活性和用户体验。

## 功能特性

### 1. 拖拽功能
- **自由拖拽**: 点击并拖拽监控器头部的 `⋮⋮` 图标或整个头部区域
- **实时反馈**: 拖拽过程中提供半透明效果和阴影反馈
- **边界限制**: 确保控件始终保持在可视区域内
- **触摸支持**: 完全支持移动设备的触摸拖拽

### 2. 边缘吸附功能
- **自动吸附**: 当控件拖拽到屏幕边缘时自动吸附
- **部分隐藏**: 吸附后只显示一小部分，节省屏幕空间
- **触发区域**: 吸附后在边缘显示触发区域，点击可重新展开
- **平滑动画**: 所有吸附和展开操作都有平滑的动画过渡

### 3. 冲突避免机制
- **自动检测**: 当两个控件位置过于接近时自动检测冲突
- **智能调整**: 自动调整位置避免重叠，优先保持用户设置的位置
- **最小距离**: 确保控件之间保持合理的最小距离

### 4. 位置记忆
- **本地存储**: 自动保存控件位置到浏览器本地存储
- **会话恢复**: 页面刷新后自动恢复上次的位置设置
- **个性化**: 每个用户的位置设置独立保存

## 使用方法

### 基本拖拽操作

1. **开始拖拽**: 
   - 鼠标：点击并按住监控器头部的拖拽手柄（⋮⋮）或头部区域
   - 触摸：长按监控器头部区域

2. **拖拽移动**:
   - 保持按住状态，移动鼠标或手指到目标位置
   - 控件会实时跟随移动，并显示半透明效果

3. **结束拖拽**:
   - 释放鼠标按钮或抬起手指
   - 控件会自动检查边缘吸附和冲突避免

### 边缘吸附操作

1. **触发吸附**:
   - 将控件拖拽到屏幕边缘（距离边缘50像素内）
   - 控件会自动吸附到最近的边缘

2. **重新展开**:
   - 点击边缘的触发区域
   - 或者将鼠标悬停在触发区域上

### 位置重置

在演示页面中提供了"重置位置"按钮，可以将所有监控器恢复到默认位置。

## 技术实现

### 核心组件

#### DraggableMixin
位置：`src/mixins/DraggableMixin.js`

这是核心的拖拽功能混入，提供了：
- 拖拽状态管理
- 位置计算和边界检测
- 边缘吸附逻辑
- 冲突检测和避免
- 位置持久化

#### 修改的组件

1. **SystemHealthMonitor** (`src/components/ui/SystemHealthMonitor.vue`)
   - 添加了DraggableMixin
   - 增加了拖拽手柄
   - 支持边缘吸附触发区域

2. **PerformanceMonitor** (`src/components/ui/PerformanceMonitor.vue`)
   - 添加了DraggableMixin
   - 增加了拖拽手柄
   - 支持边缘吸附触发区域

### 样式系统

#### 全局样式
位置：`src/assets/css/draggable.css`

提供了：
- 拖拽状态样式
- 边缘吸附样式
- 冲突高亮样式
- 响应式适配
- 无障碍支持

### 演示页面

#### DraggableMonitorsDemo
位置：`src/views/DraggableMonitorsDemo.vue`

功能演示页面，包含：
- 完整的拖拽功能演示
- 实时事件日志
- 控制面板
- 使用说明

访问路径：`/draggable-monitors`

## 配置选项

### DraggableMixin配置

```javascript
// 边缘吸附配置
snapConfig: {
  threshold: 50,        // 距离边缘多少像素时开始吸附
  hiddenSize: 30,       // 吸附后显示的大小
  animationDuration: 300 // 动画持续时间（毫秒）
}

// 冲突避免配置
conflictAvoidance: {
  enabled: true,        // 是否启用冲突避免
  minDistance: 20,      // 控件之间的最小距离
  otherComponents: []   // 其他组件列表（自动管理）
}
```

### 自定义拖拽手柄

可以通过重写`isDragHandle`方法来自定义哪些元素可以作为拖拽手柄：

```javascript
isDragHandle(target) {
  // 自定义逻辑
  return target.classList.contains('my-drag-handle');
}
```

## 响应式设计

### 移动端适配
- 在移动设备上自动隐藏拖拽手柄
- 控件自动切换为相对定位
- 禁用边缘吸附功能

### 触摸设备优化
- 增大触摸目标尺寸
- 优化触摸事件处理
- 提供触觉反馈

### 无障碍支持
- 支持高对比度模式
- 支持减少动画模式
- 键盘导航支持（计划中）

## 浏览器兼容性

- **现代浏览器**: 完全支持（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
- **移动浏览器**: 完全支持（iOS Safari 12+, Chrome Mobile 60+）
- **旧版浏览器**: 基本功能支持，部分动画效果可能降级

## 性能考虑

### 优化措施
- 使用`transform`而非`top/left`进行位置变换
- 拖拽时禁用过渡动画
- 节流处理拖拽事件
- 智能的冲突检测算法

### 内存管理
- 自动清理事件监听器
- 组件销毁时注销全局引用
- 限制事件日志条目数量

## 未来计划

### 短期计划
- [ ] 添加键盘导航支持
- [ ] 增加更多吸附选项（网格吸附、对象吸附）
- [ ] 优化移动端体验

### 长期计划
- [ ] 支持控件分组拖拽
- [ ] 添加拖拽历史记录
- [ ] 集成到其他UI组件

## 故障排除

### 常见问题

1. **拖拽不响应**
   - 检查是否正确引入了DraggableMixin
   - 确认拖拽手柄元素存在
   - 检查CSS样式是否正确加载

2. **位置不保存**
   - 检查浏览器是否支持localStorage
   - 确认组件名称设置正确
   - 检查是否有其他脚本清理localStorage

3. **移动端问题**
   - 确认触摸事件处理正确
   - 检查viewport设置
   - 验证CSS媒体查询

### 调试技巧

1. 启用事件日志查看详细操作记录
2. 使用浏览器开发者工具检查元素位置
3. 检查控制台是否有错误信息

## 贡献指南

如果您想为拖拽功能贡献代码：

1. Fork项目仓库
2. 创建功能分支
3. 编写测试用例
4. 提交Pull Request

请确保：
- 代码符合项目规范
- 添加适当的注释
- 更新相关文档
- 通过所有测试用例
